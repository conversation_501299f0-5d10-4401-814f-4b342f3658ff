import { singleton } from 'tsyringe';

import { toDbIds } from '@malou-io/package-models';

import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class ArchiveReviewsUseCase {
    constructor(private readonly _reviewsRepository: ReviewsRepository) {}

    async execute(reviewIds: string[]): Promise<{ archivedCount: number }> {
        // Perform bulk update to archive all reviews
        const result = await this._reviewsRepository.updateMany({
            filter: { _id: { $in: toDbIds(reviewIds) } },
            update: { archived: true },
        });

        return { archivedCount: result.modifiedCount };
    }
}
