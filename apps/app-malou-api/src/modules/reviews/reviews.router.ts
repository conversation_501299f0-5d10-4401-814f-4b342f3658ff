import { Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import { CreatePrivateReviewDto } from '@malou-io/package-dto';

import { casl, userHasAccessToRestaurant } from ':helpers/casl/middlewares';
import { authorize } from ':plugins/passport';

import ReviewsController from './reviews.controller';

@singleton()
export default class ReviewsRouter {
    constructor(private _reviewsController: ReviewsController) {}

    init(router: Router): void {
        /**
         * Get total restaurant reviews by rating and details by platform with filters
         *
         * @api {get} /reviews/restaurants/:restaurant_id/rating Request reviews for a restaurant
         * @apiName GetReviewsRating
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiParam {String[]} platforms
         * @apiParam {Date} startDate
         * @apiParam {Date} endDate
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *      "data": {
         *            "result": [
         *                {
         *                    "total": 24,
         *                    "platforms": [
         *                        {
         *                            "key": "tripadvisor",
         *                            "nbReviews": 10
         *                        },
         *                        {
         *                            "key": "gmb",
         *                            "nbReviews": 14
         *                        }
         *                    ],
         *                    "rating": 1
         *                },
         *            ],
         *          "startDate": "2020-11-02T00:00:00Z",
         *          "endDate": "2020-11-02T23:59:59Z"
         *       }
         *
         */
        router.get('/reviews/restaurants/:restaurant_id/chart/rating', authorize(), userHasAccessToRestaurant, (req, res, next) =>
            this._reviewsController.handleGetReviewsRating(req, res, next)
        );

        /**
         * Get average rating of one restaurant
         *
         * @api {get} /reviews/restaurants/:restaurant_id/chart/rating/average Request reviews for a restaurant
         * @apiName GetReviewsRatingAverage
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiParam {String[]} platforms
         * @apiParam {Date} startDate
         * @apiParam {Date} endDate
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *      "data": {
         *          "results": [
         *              "restaurantId": 1234,
         *              "averageRating": 4.0037489
         *          ]
         *          "startDate": '2020-11-02T00:00:00Z',
         *          "endDate": '2020-11-02T23:59:59Z'
         *      }
         *   }
         *
         */
        router.get('/reviews/restaurants/:restaurant_id/chart/rating/average', authorize(), userHasAccessToRestaurant, (req, res, next) =>
            this._reviewsController.handleGetRestaurantsReviewsRatingAverage(req, res, next)
        );

        /**
         * Get total replied and not replied reviews for a specific period.
         *
         * @api {get} /reviews/restaurants/:restaurant_id/chart/replied Request reviews for a restaurant
         * @apiName GetReviewsChartReplied
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiParam {String[]} platforms
         * @apiParam {Date} startDate
         * @apiParam {Date} endDate
         *
         */
        router.get('/reviews/restaurants/:restaurant_id/chart/replied', authorize(), userHasAccessToRestaurant, (req, res, next) =>
            this._reviewsController.handleGetAnsweredAndNotAnsweredReviews(req, res, next)
        );

        /**
         * Get restaurant reviews evolutions by week with filters
         *
         * @api {get} /reviews/restaurants/:restaurant_id/evolution Request reviews for a restaurant
         * @apiName GetReviewsEvolution
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiParam {String[]} platforms
         * @apiParam {Date} startDate
         * @apiParam {Date} endDate
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *      "data": {
         *          "result" :  [
         *              {
         *                  "key": "gmb",
         *                  "year": 2020,
         *                  "week": 25,
         *                  "total": 1
         *              },
         *              ...
         *          ],
         *          "startDate": "2020-11-02T00:00:00Z",
         *          "endDate": "2020-11-02T23:59:59Z"
         *       }
         *   }
         *
         */
        router.get('/reviews/restaurants/:restaurant_id/chart/evolution', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewsEvolution(req, res, next)
        );

        /**
         * Get total restaurant reviews about two dates with filters, if dates are null return all reviews.
         *
         * @api {get} /reviews/restaurants/:restaurant_id/evolution/compare Request reviews for a restaurant
         * @apiName GetReviewsEvolutionCompare
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiParam {String[]} platforms
         * @apiParam {Date} startDate
         * @apiParam {Date} endDate
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *      "data": {
         *          "results": [
         *              {
         *                  "restaurantId": 1234,
         *                  "total": 100
         *              }
         *          ],
         *          "startDate": "2020-11-02T00:00:00Z",
         *          "endDate": "2020-11-02T23:59:59Z"
         *      }
         *   }
         *
         */
        router.get('/reviews/restaurants/:restaurant_id/chart/evolution/total', authorize(), userHasAccessToRestaurant, (req, res, next) =>
            this._reviewsController.handleGetReviewsEvolutionTotal(req, res, next)
        );

        router.post('/reviews/restaurants/chart/evolution/total', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewsEvolutionTotalInDateRange(req, res, next)
        );

        router.get(
            '/reviews/restaurants/:restaurant_id/chart/average-answer-time',
            authorize(),
            userHasAccessToRestaurant,
            (req, res, next) => this._reviewsController.handleGetAnswerTime(req, res, next)
        );

        router.get('/reviews/restaurants/:restaurant_id/chart/review_analyses', this._reviewsController.handleGetReviewsWithAnalysis);

        /**
         * Check if a review can be answered
         */
        router.get('/reviews/restaurants/:restaurant_id/can_answer', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleCanAnswer(req, res, next)
        );

        router.get('/reviews/restaurants/:restaurant_id/platforms/:platform_key/count', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewTotalCountForPlatform(req, res, next)
        );

        /**
         * Get reviews with segment analyses for csv download(light)
         */
        router.get('/reviews/restaurants/:restaurant_id/segment-analyses', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewsWithSegmentAnalyses(req, res, next)
        );

        /**
         * Get a review related to a comment option
         */
        router.get('/reviews/restaurants/:restaurant_id/comment-option', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewRelatedToCommentOption(req, res, next)
        );

        /**
         *
         * @api {get} /restaurants/synchronize Pull reviews and restaurants
         * @apiName PullReviews
         * @apiGroup Reviews
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Started synchronization"
         *   }
         *
         * @apiError Bad request, need restaurants IDs
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_ids"
         *     }
         *   }
         */
        router.get('/reviews/restaurants/synchronize', authorize(), (req, res, next) =>
            this._reviewsController.handleSynchronizeReviews(req, res, next)
        );

        /**
         * Get reviews with filters and pagination
         *
         * Supposed to replace `GET /reviews`. Unlike `GET /reviews`, this route
         * does not return the total number of reviews since it’s quite expensive
         * to compute: consider using `GET /reviews/count`.
         */
        router.get('/reviews/v2', authorize(), (req, res, next) => this._reviewsController.handleGetRestaurantsReviewsV2(req, res, next));

        /**
         * Count reviews with filters.
         */
        router.post('/reviews/count', authorize(), (req, res, next) => this._reviewsController.handleGetReviewCount(req, res, next));

        /**
         * Get count of all unanswered reviews with filters
         * @deprecated use /unanswered/v2
         */
        router.get('/reviews/unanswered', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleGetRestaurantsUnansweredCount(req, res, next)
        );

        /**
         * Get count of all unanswered reviews with filters
         */
        router.post('/reviews/unanswered/v2', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleGetRestaurantsUnansweredCountV2(req, res, next)
        );

        /**
         * @api {put} /reviews/reviewsLastUpdate/:restaurant_id set last synchronize date of reviews to current date
         * @apiName
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "synchronize date updated."
         *   }
         *
         * @apiError Bad , need restaurant_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_id"
         *     }
         *
         * @apiError Internal Server Error for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "error": {
         *           errors: {},
         *           message: 'xxx',
         *           name: "HttpErrorResponse",
         *           status: 500
         *         }
         *     }
         */
        router.put('/reviews/reviewsLastUpdate/:restaurant_id', authorize(), (req, res, next) =>
            this._reviewsController.handleSetReviewsLastUpdate(req, res, next)
        );

        /**
         *
         * @api {delete} /reviews/platforms/:platform_key/restaurants/:restaurant_id/ Delete reviews for a specific platform
         * @apiName DeleteReviewsForPlatform
         * @apiGroup Reviews
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} platform_key Platform key.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Reviews deleted"
         *   }
         *
         * @apiError Bad , need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_id and platform_key"
         *     }
         * @apiError Internal Server Error for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "msg": {
         *           error: true,
         *           message: 'xxx',
         *           errorData: 'xxx'
         *         }
         *     }
         */
        router.delete('/reviews/platforms/:platform_key/restaurants/:restaurant_id', authorize(), (req, res, next) =>
            this._reviewsController.handleDeleteReviewsForPlatform(req, res, next)
        );

        /**
         * Download reviews as pdf
         * @deprecated use /download-reviews-as-pdf/v2
         */
        router.get('/reviews/download-reviews-as-pdf', authorize(), (req, res, next) =>
            this._reviewsController.handleGetFilteredReviewsAsPdf(req, res, next)
        );

        /**
         * Download reviews as pdf
         */
        router.post('/reviews/download-reviews-as-pdf/v2', authorize(), (req, res, next) =>
            this._reviewsController.handleGetFilteredReviewsAsPdfV2(req, res, next)
        );

        /**
         * Get review by socialIds and restaurantIds
         */
        router.post('/reviews/social_ids', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewBySocialIds(req, res, next)
        );

        /**
         * Get reviews by topic
         */
        router.post('/reviews/topic', authorize(), (req, res, next) => this._reviewsController.handleGetReviewsByTopic(req, res, next));

        /**
         * Update review's archived value
         */
        router.put('/reviews/:review_id/archived', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleUpdateReviewArchivedValue(req, res, next)
        );

        /**
         * Archive multiple reviews
         */
        router.post('/reviews/archive', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleArchiveReviews(req, res, next)
        );

        // TODO: @Tanguy delete when multi agent will be live on mobile app also
        /**
         * Fetch and update review with ai relevant bricks
         */
        router.get('/reviews/:review_id/fetch-relevant-bricks', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleFetchAiRelevantBricks(req, res, next)
        );

        /**
         * Get review page
         * @deprecated use /:review_id/page/v2
         */
        router.get('/reviews/:review_id/page', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleGetReviewPage(req, res, next)
        );

        /**
         * Get review page
         */
        router.post('/reviews/:review_id/page/v2', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleGetReviewPageV2(req, res, next)
        );

        /**
         * Publish a comment on a review's social platform
         */
        router.post('/reviews/:review_id/comments/restaurants/:restaurant_id', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handlePublishComment(req, res, next)
        );

        /**
         * Update a comment on a review's social platform
         */
        router.put('/reviews/:review_id/comments/:comment_id/restaurants/:restaurant_id', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleUpdateComment(req, res, next)
        );

        /**
         *
         * @api {delete} /reviews/review_id Delete a specific reviews
         * @apiName DeleteReview
         * @apiGroup Reviews
         *
         * @apiParam {string} review_id Review's unique ID.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Review deleted"
         *   }
         *
         * @apiError Bad , need review_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need review_id"
         *     }
         *
         */
        router.delete('/reviews/:review_id', authorize(), (req, res, next) => this._reviewsController.handleDeleteReview(req, res, next));

        /**
         * Get review by id
         */
        router.get('/reviews/:review_id', authorize(), (req, res, next) => this._reviewsController.handleGetReviewById(req, res, next));

        /**
         * Get reviewer name validation for a review
         */
        router.get('/reviews/:review_id/reviewer-name-validation', authorize(), (req, res, next) =>
            this._reviewsController.handleGetReviewerNameValidation(req, res, next)
        );

        // -------------------------------------------------------------------------------------------------------------
        // Private Reviews
        // -------------------------------------------------------------------------------------------------------------

        /**
         * Publish a comment on a private review and send an email
         */
        router.post('/reviews/:review_id/comments/restaurants/:restaurant_id/private', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handlePublishPrivateReviewComment(req, res, next)
        );

        router.post('/reviews/private', (req: Request<never, never, CreatePrivateReviewDto>, res: Response, next) =>
            this._reviewsController.handlePublishPrivateReview(req, res, next)
        );

        router.post('/reviews/private/:private_review_id/client', authorize(), (req, res, next) =>
            this._reviewsController.handleLinkClientIdOrEmailToPrivateReview(req, res, next)
        );

        router.put('/reviews/:review_id/keywords_lang', authorize(), (req, res, next) =>
            this._reviewsController.handleUpdateKeywordsLang(req, res, next)
        );

        router.post('/reviews/:review_id/translations', authorize(), casl(), (req, res, next) =>
            this._reviewsController.handleAddTranslationToReview(req, res, next)
        );
    }
}
