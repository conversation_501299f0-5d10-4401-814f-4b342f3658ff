import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { IUser, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, Role, UserCaslRole } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { CreateNewAccountService } from ':modules/users/services/create-new-account.service';
import { UsersRepository } from ':modules/users/users.repository';
import { NewOrganizationEvent } from ':modules/webhooks/malou/validators/new-organization.validators';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class CreateNewOrganizationUseCase {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _createNewAccountService: CreateNewAccountService,
        @inject(InjectionToken.SubscriptionsProvider) private readonly _subscriptionsProvider: SubscriptionsProvider,
        private readonly _slackAlertService: SlackService
    ) {}

    async execute(event: NewOrganizationEvent): Promise<void> {
        let createdOrganization: any = null;
        let updatedSubscriptionsProviderLocation = false;
        const currentOrganization = await this._organizationsRepository.findOne({
            filter: { subscriptionsProviderId: event.organizationProviderId },
            options: { lean: true },
        });

        try {
            if (!currentOrganization) {
                createdOrganization = await this._organizationsRepository.create({
                    data: {
                        name: event.organizationName,
                        subscriptionsProviderId: event.organizationProviderId,
                    },
                });
            }
            const organizationId = createdOrganization?._id ?? currentOrganization?._id;
            assert(organizationId, 'Organization should exist now');

            await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                subscriptionsProviderLocationId: event.organizationProviderId,
                malouRestaurantId: organizationId.toString(),
            });

            updatedSubscriptionsProviderLocation = true;

            for (const user of event.users) {
                const account: Partial<IUser> = {
                    email: user.email,
                    subscriptionsProviderId: user.email,
                    defaultLanguage: event.usersLang ?? ApplicationLanguage.EN,
                    organizationIds: [organizationId._id],
                    role: Role.MALOU_BASIC,
                    caslRole: UserCaslRole.OWNER,
                };
                const existingUser = await this._usersRepository.findOne({
                    filter: { email: user.email },
                    options: { lean: true },
                });
                if (existingUser) {
                    await this._usersRepository.updateOne({
                        filter: { _id: existingUser._id },
                        update: { $addToSet: { organizationIds: organizationId._id } },
                    });
                    continue;
                }
                await this._createNewAccountService
                    .createAccount({
                        user: account,
                        verified: false,
                        sendConfirmEmail: true,
                    })
                    .catch((err) => {
                        // For safety purposes, we log the error but we don't throw it. This is to avoid a rollback of the organization creation
                        // in case of an error for a single user, we still want to create the organization and the other users.
                        // This error should never happen
                        logger.error('[CreateNewOrganizationUseCase] Error creating user', err);
                        this._slackAlertService.sendAlert({
                            data: { err },
                            channel: SlackChannel.APP_ALERTS,
                        });
                    });
            }
        } catch (err) {
            // Could happen if subscriptions provider would throw randomly
            // We need to make sure we did not create the organization if it was not properly linked to Hyperline
            // Should never happen though
            logger.error('[CreateNewOrganizationUseCase] Error creating organization', err);
            if (createdOrganization) {
                await this._organizationsRepository.deleteOne({
                    filter: { _id: toDbId(createdOrganization._id.toString()) },
                });
                if (updatedSubscriptionsProviderLocation) {
                    // If this was a new organization, we roll back to nothing linked to malou on Hyperline
                    await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                        subscriptionsProviderLocationId: event.organizationProviderId,
                        malouRestaurantId: null,
                    });
                }
            }
            throw err;
        }
    }
}
