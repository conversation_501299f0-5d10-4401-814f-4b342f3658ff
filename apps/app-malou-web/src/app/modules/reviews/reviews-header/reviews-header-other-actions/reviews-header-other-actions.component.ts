import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { forkJoin, switchMap } from 'rxjs';

import { ReviewsService } from ':modules/reviews/reviews.service';
import { ReviewStrategyType } from ':modules/reviews/reviews/reviews.strategy';
import * as ReviewsActions from ':modules/reviews/store/reviews.actions';
import { selectHasLoadedAllReviews, selectReviews, selectReviewsFilters } from ':modules/reviews/store/reviews.selectors';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-reviews-header-other-actions',
    templateUrl: './reviews-header-other-actions.component.html',
    styleUrls: ['./reviews-header-other-actions.component.scss'],
    imports: [MatButtonModule, MatIconModule, MatMenuModule, MatTooltipModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsHeaderOtherActionsComponent {
    readonly SvgIcon = SvgIcon;
    private readonly _store = inject(Store);
    private readonly _reviewsService = inject(ReviewsService);

    archiveAllReviews(): void {
        forkJoin([
            this._store
                .select(selectReviews)
                .pipe(switchMap((reviews) => this._reviewsService.archiveAllReviews(reviews.map((r) => r._id)))),
            this._store.select(selectReviewsFilters),
            this._store.select(selectHasLoadedAllReviews),
        ]).subscribe({
            next: ([, filters, hasLoadedAllReviews]) => {
                this._store.dispatch(ReviewsActions.archiveAllReviews());
                if (!filters.archived && !hasLoadedAllReviews) {
                    this._store.dispatch(ReviewsActions.getOrFetchReviews({ strategyType: ReviewStrategyType.REPLACE, reviewId: '' }));
                }
            },
            error: (err) => {
                console.warn('err :>>', err);
            },
        });
        // this._store
        //     .select(selectReviews)
        //     .pipe(
        //         switchMap((reviews) => this._reviewsService.archiveAllReviews(reviews.map((r) => r._id)))
        //     )
        // this._reviewsService.archiveAllReviews();
        // this._store.dispatch(ReviewsActions.archiveAllReviews());
        // this._store.dispatch(ReviewsActions.getOrFetchReviews({ strategyType: ReviewStrategyType.REPLACE, reviewId: '' }));
    }
}
